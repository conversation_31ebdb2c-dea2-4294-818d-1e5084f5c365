import { ConfigLive } from '@/infrastructure/config/config.live';
import { DBSchema } from '@rie/db-schema';
import { Database, Database as PgDatabase } from '@rie/postgres-db';
import { eq } from 'drizzle-orm';
import * as Effect from 'effect/Effect';
import * as Layer from 'effect/Layer';

const PgDatabaseLive = Layer.unwrapEffect(
  ConfigLive.pipe(
    Effect.map((envVars) =>
      PgDatabase.pgLayer({
        url: envVars.PG_DATABASE_URL,
        ssl: envVars.ENV === 'prod',
      }),
    ),
  ),
).pipe(Layer.provide(ConfigLive.Default));

export class BuildingsRepositoryLive extends Effect.Service<BuildingsRepositoryLive>()(
  'BuildingsRepositoryLive',
  {
    dependencies: [PgDatabaseLive],
    effect: Effect.gen(function* () {
      const dbClient = yield* Database.PgDatabase;

      const findAllBuildings = dbClient.makeQuery((execute) => {
        return execute((client) =>
          client.query.buildings.findMany({
            columns: {
              id: true,
              campusId: true,
              civicAddressId: true,
              sadId: true,
              diId: true,
              createdAt: true,
              updatedAt: true,
              modifiedBy: true,
            },
            with: {
              translations: {
                columns: {
                  id: true,
                  locale: true,
                  name: true,
                  description: true,
                  otherNames: true,
                },
              },
            },
          }),
        );
      });

      const findBuildingById = dbClient.makeQuery((execute, id: string) => {
        return execute((client) =>
          client.query.buildings.findFirst({
            where: eq(DBSchema.buildings.id, id),
            columns: {
              id: true,
              campusId: true,
              civicAddressId: true,
              sadId: true,
              diId: true,
              createdAt: true,
              updatedAt: true,
              modifiedBy: true,
            },
            with: {
              translations: {
                columns: {
                  id: true,
                  locale: true,
                  name: true,
                  description: true,
                  otherNames: true,
                },
              },
            },
          }),
        );
      });

      const createBuilding = (params: {
        building: {
          campusId?: string | null;
          civicAddressId?: string | null;
          sadId?: string | null;
          diId?: string | null;
        };
        translations: Array<{
          locale: string;
          name?: string | null;
          description?: string | null;
          otherNames?: string | null;
        }>;
      }) => {
        return dbClient.transaction((tx) => {
          return Effect.gen(function* () {
            // First create the building
            const [building] = yield* tx((client) =>
              client
                .insert(DBSchema.buildings)
                .values({
                  ...params.building,
                })
                .returning({
                  id: DBSchema.buildings.id,
                  campusId: DBSchema.buildings.campusId,
                  civicAddressId: DBSchema.buildings.civicAddressId,
                  sadId: DBSchema.buildings.sadId,
                  diId: DBSchema.buildings.diId,
                  createdAt: DBSchema.buildings.createdAt,
                  updatedAt: DBSchema.buildings.updatedAt,
                  modifiedBy: DBSchema.buildings.modifiedBy,
                }),
            );

            // Then create the translations
            const translationsToInsert = params.translations.map(
              (translation) => ({
                dataId: building.id,
                ...translation,
              }),
            );
            yield* tx((client) =>
              client
                .insert(DBSchema.buildingsI18N)
                .values(translationsToInsert),
            );

            // Fetch and return the complete building with translations
            const buildingWithTranslations = yield* tx((client) =>
              client.query.buildings.findFirst({
                where: eq(DBSchema.buildings.id, building.id),
                columns: {
                  id: true,
                  campusId: true,
                  civicAddressId: true,
                  sadId: true,
                  diId: true,
                  createdAt: true,
                  updatedAt: true,
                  modifiedBy: true,
                },
                with: {
                  translations: {
                    columns: {
                      id: true,
                      locale: true,
                      name: true,
                      description: true,
                      otherNames: true,
                    },
                  },
                },
              }),
            );

            return buildingWithTranslations;
          });
        });
      };

      const updateBuilding = (params: {
        buildingId: string;
        building: {
          campusId?: string | null;
          civicAddressId?: string | null;
          sadId?: string | null;
          diId?: string | null;
          modifiedBy: string;
        };
        translations: Array<{
          locale: string;
          name?: string | null;
          description?: string | null;
          otherNames?: string | null;
          acronyms?: string | null;
        }>;
      }) => {
        return dbClient.transaction((tx) => {
          return Effect.gen(function* () {
            // First update the building
            yield* tx((client) =>
              client
                .update(DBSchema.buildings)
                .set({
                  ...params.building,
                })
                .where(eq(DBSchema.buildings.id, params.buildingId))
                .returning({
                  id: DBSchema.buildings.id,
                  campusId: DBSchema.buildings.campusId,
                  civicAddressId: DBSchema.buildings.civicAddressId,
                  sadId: DBSchema.buildings.sadId,
                  diId: DBSchema.buildings.diId,
                  createdAt: DBSchema.buildings.createdAt,
                  updatedAt: DBSchema.buildings.updatedAt,
                  modifiedBy: DBSchema.buildings.modifiedBy,
                }),
            );

            // Then update translations
            // Delete existing translations
            yield* tx((client) =>
              client
                .delete(DBSchema.buildingsI18N)
                .where(eq(DBSchema.buildingsI18N.dataId, params.buildingId)),
            );

            // Insert new translations
            const translationsToInsert = params.translations.map(
              (translation) => ({
                dataId: params.buildingId,
                ...translation,
              }),
            );

            yield* tx((client) =>
              client
                .insert(DBSchema.buildingsI18N)
                .values(translationsToInsert),
            );

            // Fetch and return the complete building with translations
            const buildingWithTranslations = yield* tx((client) =>
              client.query.buildings.findFirst({
                where: eq(DBSchema.buildings.id, params.buildingId),
                columns: {
                  id: true,
                  campusId: true,
                  civicAddressId: true,
                  sadId: true,
                  diId: true,
                  createdAt: true,
                  updatedAt: true,
                  modifiedBy: true,
                },
                with: {
                  translations: {
                    columns: {
                      id: true,
                      locale: true,
                      name: true,
                      description: true,
                      otherNames: true,
                    },
                  },
                },
              }),
            );

            return buildingWithTranslations;
          });
        });
      };

      const deleteBuilding = dbClient.makeQuery((execute, id: string) => {
        return execute((client) =>
          client
            .delete(DBSchema.buildings)
            .where(eq(DBSchema.buildings.id, id))
            .returning({ id: DBSchema.buildings.id }),
        );
      });

      return {
        findAllBuildings,
        findBuildingById,
        createBuilding,
        updateBuilding,
        deleteBuilding,
      } as const;
    }),
  },
) { }
